package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// =====================================================
// 广告位置管理
// =====================================================

// 广告位置列表
type AdvertisementPositionListReq struct {
	g.Meta `path:"/advertisement/position/list" tags:"广告管理" method:"post" summary:"广告位置列表"`
}

type AdvertisementPositionListRes struct {
	List []AdvertisementPositionItem `json:"list" dc:"广告位置列表"`
}

type AdvertisementPositionItem struct {
	Id           int    `json:"id" dc:"主键ID"`
	PositionName string `json:"positionName" dc:"广告位名称"`
	PositionCode string `json:"positionCode" dc:"位置编码"`
	Remark       string `json:"remark" dc:"备注"`
	CreateTime   int64  `json:"createTime" dc:"创建时间"`
	UpdateTime   int64  `json:"updateTime" dc:"更新时间"`
}

// 修改广告位置备注
type AdvertisementPositionEditReq struct {
	g.Meta `path:"/advertisement/position/edit" tags:"广告管理" method:"post" summary:"修改广告位置备注"`
	Id     int    `json:"id" v:"required|min:1#请选择广告位置|广告位置ID错误" dc:"广告位置ID"`
	Remark string `json:"remark" v:"max-length:500#备注长度不能超过500字符" dc:"备注"`
}

type AdvertisementPositionEditRes struct {
	*EmptyDataRes
}

// =====================================================
// 广告管理
// =====================================================

// 广告列表
type AdvertisementListReq struct {
	g.Meta       `path:"/advertisement/list" tags:"广告管理" method:"post" summary:"广告列表"`
	Name         *string `json:"name" dc:"广告名称"`
	PositionCode *string `json:"positionCode" dc:"广告位置编码"`
	Status       *int    `json:"status" dc:"状态: 0-禁用, 1-启用"`
	ListReq
}

type AdvertisementListRes struct {
	ListRes
	List []AdvertisementListItem `json:"list" dc:"广告列表"`
}

type AdvertisementListItem struct {
	Id           int               `json:"id" dc:"主键ID"`
	PositionCode string            `json:"positionCode" dc:"广告位置编码"`
	PositionName string            `json:"positionName" dc:"广告位置名称"`
	Title        string            `json:"title" dc:"广告名称"`
	Status       int               `json:"status" dc:"状态: 0-禁用, 1-启用"`
	StatusText   string            `json:"statusText" dc:"状态文本"`
	SortOrder    int               `json:"sortOrder" dc:"排序权重"`
	StartTime    int64             `json:"startTime" dc:"开始时间"`
	EndTime      int64             `json:"endTime" dc:"结束时间"`
	CreateTime   int64             `json:"createTime" dc:"创建时间"`
	UpdateTime   int64             `json:"updateTime" dc:"更新时间"`
	LanguageArr  []LanguageArrItem `json:"languageArr" dc:"多语言支持信息"`
}

// 广告详情
type AdvertisementOneReq struct {
	g.Meta `path:"/advertisement/one" tags:"广告管理" method:"post" summary:"广告详情"`
	Id     int `json:"id" v:"required|min:1#请选择广告|广告ID错误" dc:"广告ID"`
}

type AdvertisementOneRes struct {
	Advertisement AdvertisementDetail `json:"advertisement" dc:"广告详情"`
}

type AdvertisementDetail struct {
	Id           int                           `json:"id" dc:"主键ID"`
	PositionCode string                        `json:"positionCode" dc:"广告位置编码"`
	SortOrder    int                           `json:"sortOrder" dc:"排序权重"`
	Status       int                           `json:"status" dc:"状态: 0-禁用, 1-启用"`
	StartTime    int64                         `json:"startTime" dc:"开始时间"`
	EndTime      int64                         `json:"endTime" dc:"结束时间"`
	CreateTime   int64                         `json:"createTime" dc:"创建时间"`
	UpdateTime   int64                         `json:"updateTime" dc:"更新时间"`
	Languages    []AdvertisementLanguageDetail `json:"languages" dc:"多语言内容"`
	Banners      []BannerDetail                `json:"banners" dc:"Banner图片"`
}

type AdvertisementLanguageDetail struct {
	LanguageId       int    `json:"languageId" dc:"语言ID"`
	LanguageText     string `json:"languageText" dc:"语言文本"`
	Title            string `json:"title" dc:"广告名称"`
	Description      string `json:"description" dc:"广告描述"`
	DisplayType      int    `json:"displayType" dc:"显示类型: 1-单图固定, 2-多图轮播"`
	DisplayTypeText  string `json:"displayTypeText" dc:"显示类型文本"`
	CarouselInterval int    `json:"carouselInterval" dc:"轮播间隔时间(秒)"`
}

type BannerDetail struct {
	Id         int    `json:"id" dc:"Banner ID"`
	LanguageId int    `json:"languageId" dc:"语言ID"`
	ImageUrl   string `json:"imageUrl" dc:"图片URL"`
	LinkUrl    string `json:"linkUrl" dc:"跳转链接"`
	SortOrder  int    `json:"sortOrder" dc:"排序权重"`
	Status     int    `json:"status" dc:"状态: 0-禁用, 1-启用"`
}

// 新增广告
type AdvertisementAddReq struct {
	g.Meta       `path:"/advertisement/add" tags:"广告管理" method:"post" summary:"新增广告"`
	PositionCode string                     `json:"positionCode" v:"required#请选择广告位置" dc:"广告位置编码"`
	SortOrder    int                        `json:"sortOrder" v:"min:0#排序权重不能小于0" dc:"排序权重"`
	StartTime    int64                      `json:"startTime" v:"required|min:1#请设置开始时间|开始时间错误" dc:"开始时间"`
	EndTime      int64                      `json:"endTime" v:"required|min:1#请设置结束时间|结束时间错误" dc:"结束时间"`
	Languages    []AdvertisementLanguageReq `json:"languages" v:"required|length:1,3#请设置多语言内容|多语言内容数量错误" dc:"多语言内容"`
	Banners      []BannerReq                `json:"banners" v:"required|min-length:1#请上传Banner图片|至少需要一张Banner图片" dc:"Banner图片"`
}

type AdvertisementLanguageReq struct {
	LanguageId       int    `json:"languageId" v:"required|in:0,1,2#请选择语言|语言类型错误" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	Title            string `json:"title" v:"required|max-length:255#请输入广告名称|广告名称长度不能超过255字符" dc:"广告名称"`
	Description      string `json:"description" v:"max-length:1000#广告描述长度不能超过1000字符" dc:"广告描述"`
	DisplayType      int    `json:"displayType" v:"required|in:1,2#请选择显示类型|显示类型错误" dc:"显示类型: 1-单图固定, 2-多图轮播"`
	CarouselInterval int    `json:"carouselInterval" v:"min:1|max:60#轮播间隔时间范围1-60秒|轮播间隔时间范围1-60秒" dc:"轮播间隔时间(秒)"`
}

type BannerReq struct {
	LanguageId int    `json:"languageId" v:"required|in:0,1,2#请选择语言|语言类型错误" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	ImageUrl   string `json:"imageUrl" v:"required|url#请上传图片|图片URL格式错误" dc:"图片URL"`
	LinkUrl    string `json:"linkUrl" v:"url#跳转链接格式错误" dc:"跳转链接"`
	SortOrder  int    `json:"sortOrder" v:"min:0#排序权重不能小于0" dc:"排序权重"`
}

type AdvertisementAddRes struct {
	Id int `json:"id" dc:"广告ID"`
}

// 编辑广告
type AdvertisementEditReq struct {
	g.Meta       `path:"/advertisement/edit" tags:"广告管理" method:"post" summary:"编辑广告"`
	Id           int                        `json:"id" v:"required|min:1#请选择广告|广告ID错误" dc:"广告ID"`
	PositionCode string                     `json:"positionCode" v:"required#请选择广告位置" dc:"广告位置编码"`
	SortOrder    int                        `json:"sortOrder" v:"min:0#排序权重不能小于0" dc:"排序权重"`
	StartTime    int64                      `json:"startTime" v:"required|min:1#请设置开始时间|开始时间错误" dc:"开始时间"`
	EndTime      int64                      `json:"endTime" v:"required|min:1#请设置结束时间|结束时间错误" dc:"结束时间"`
	Languages    []AdvertisementLanguageReq `json:"languages" v:"required|length:1,3#请设置多语言内容|多语言内容数量错误" dc:"多语言内容"`
	Banners      []BannerReq                `json:"banners" v:"required|min-length:1#请上传Banner图片|至少需要一张Banner图片" dc:"Banner图片"`
}

type AdvertisementEditRes struct {
	*EmptyDataRes
}

// 删除广告
type AdvertisementDeleteReq struct {
	g.Meta `path:"/advertisement/delete" tags:"广告管理" method:"post" summary:"删除广告"`
	Id     int `json:"id" v:"required|min:1#请选择广告|广告ID错误" dc:"广告ID"`
}

type AdvertisementDeleteRes struct {
	*EmptyDataRes
}

// 上线广告
type AdvertisementOnlineReq struct {
	g.Meta `path:"/advertisement/online" tags:"广告管理" method:"post" summary:"上线广告"`
	Id     int `json:"id" v:"required|min:1#请选择广告|广告ID错误" dc:"广告ID"`
}

type AdvertisementOnlineRes struct {
	*EmptyDataRes
}

// 下线广告
type AdvertisementOfflineReq struct {
	g.Meta `path:"/advertisement/offline" tags:"广告管理" method:"post" summary:"下线广告"`
	Id     int `json:"id" v:"required|min:1#请选择广告|广告ID错误" dc:"广告ID"`
}

type AdvertisementOfflineRes struct {
	*EmptyDataRes
}

// 广告位置选项
type AdvertisementPositionOptionsReq struct {
	g.Meta `path:"/advertisement/position/options" tags:"广告管理" method:"post" summary:"广告位置选项"`
}

type AdvertisementPositionOptionsRes struct {
	List []SelectOption `json:"list" dc:"选项列表"`
}

// 通用选项结构
type SelectOption struct {
	Label string `json:"label" dc:"显示文本"`
	Value string `json:"value" dc:"选项值"`
}
