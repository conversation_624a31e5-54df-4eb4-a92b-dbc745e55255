package advertisement

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// =====================================================
// 广告位置管理
// =====================================================

// PositionList 广告位置列表
func (c *Controller) PositionList(ctx context.Context, req *v1.AdvertisementPositionListReq) (res *v1.AdvertisementPositionListRes, err error) {
	res, err = service.Advertisement().PositionList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// PositionEdit 修改广告位置备注
func (c *Controller) PositionEdit(ctx context.Context, req *v1.AdvertisementPositionEditReq) (res *v1.AdvertisementPositionEditRes, err error) {
	res, err = service.Advertisement().PositionEdit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// PositionOptions 广告位置选项
func (c *Controller) PositionOptions(ctx context.Context, req *v1.AdvertisementPositionOptionsReq) (res *v1.AdvertisementPositionOptionsRes, err error) {
	res, err = service.Advertisement().PositionOptions(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// =====================================================
// 广告管理
// =====================================================

// List 广告列表
func (c *Controller) List(ctx context.Context, req *v1.AdvertisementListReq) (res *v1.AdvertisementListRes, err error) {
	res, err = service.Advertisement().List(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// One 广告详情
func (c *Controller) One(ctx context.Context, req *v1.AdvertisementOneReq) (res *v1.AdvertisementOneRes, err error) {
	res, err = service.Advertisement().One(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Add 新增广告
func (c *Controller) Add(ctx context.Context, req *v1.AdvertisementAddReq) (res *v1.AdvertisementAddRes, err error) {
	res, err = service.Advertisement().Add(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Edit 编辑广告
func (c *Controller) Edit(ctx context.Context, req *v1.AdvertisementEditReq) (res *v1.AdvertisementEditRes, err error) {
	res, err = service.Advertisement().Edit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Delete 删除广告
func (c *Controller) Delete(ctx context.Context, req *v1.AdvertisementDeleteReq) (res *v1.AdvertisementDeleteRes, err error) {
	res, err = service.Advertisement().Delete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Online 上线广告
func (c *Controller) Online(ctx context.Context, req *v1.AdvertisementOnlineReq) (res *v1.AdvertisementOnlineRes, err error) {
	res, err = service.Advertisement().Online(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Offline 下线广告
func (c *Controller) Offline(ctx context.Context, req *v1.AdvertisementOfflineReq) (res *v1.AdvertisementOfflineRes, err error) {
	res, err = service.Advertisement().Offline(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
