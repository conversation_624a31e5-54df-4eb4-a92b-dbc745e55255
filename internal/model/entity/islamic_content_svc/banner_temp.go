// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// BannerTemp is the golang structure for table banner_temp.
type BannerTemp struct {
	Id          uint   `json:"id"          orm:"id"          description:"主键ID"`
	LanguageId  uint   `json:"languageId"  orm:"language_id" description:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	BannerType  string `json:"bannerType"  orm:"banner_type" description:"Banner类型: home， ibadah"`
	Title       string `json:"title"       orm:"title"       description:"广告标题"`
	Description string `json:"description" orm:"description" description:"广告描述"`
	ImageUrl    string `json:"imageUrl"    orm:"image_url"   description:"广告图片URL"`
	LinkUrl     string `json:"linkUrl"     orm:"link_url"    description:"跳转链接URL"`
	SortOrder   uint   `json:"sortOrder"   orm:"sort_order"  description:"排序权重，数字越小越靠前"`
	Status      uint   `json:"status"      orm:"status"      description:"状态: 0-禁用, 1-启用"`
	StartTime   uint64 `json:"startTime"   orm:"start_time"  description:"开始时间戳(毫秒)"`
	EndTime     uint64 `json:"endTime"     orm:"end_time"    description:"结束时间戳(毫秒)"`
	AdminId     uint   `json:"adminId"     orm:"admin_id"    description:"创建管理员ID"`
	CreateTime  uint64 `json:"createTime"  orm:"create_time" description:"创建时间(毫秒时间戳)"`
	UpdateTime  uint64 `json:"updateTime"  orm:"update_time" description:"更新时间(毫秒时间戳)"`
}
