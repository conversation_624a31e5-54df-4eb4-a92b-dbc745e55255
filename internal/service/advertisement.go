// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IAdvertisement interface {
		// PositionList 广告位置列表
		PositionList(ctx context.Context, req *v1.AdvertisementPositionListReq) (res *v1.AdvertisementPositionListRes, err error)
		// PositionEdit 修改广告位置备注
		PositionEdit(ctx context.Context, req *v1.AdvertisementPositionEditReq) (res *v1.AdvertisementPositionEditRes, err error)
		// PositionOptions 广告位置选项
		PositionOptions(ctx context.Context, req *v1.AdvertisementPositionOptionsReq) (res *v1.AdvertisementPositionOptionsRes, err error)
		// List 广告列表
		List(ctx context.Context, req *v1.AdvertisementListReq) (res *v1.AdvertisementListRes, err error)
		// One 广告详情
		One(ctx context.Context, req *v1.AdvertisementOneReq) (res *v1.AdvertisementOneRes, err error)
		// Add 新增广告
		Add(ctx context.Context, req *v1.AdvertisementAddReq) (res *v1.AdvertisementAddRes, err error)
		// Edit 编辑广告
		Edit(ctx context.Context, req *v1.AdvertisementEditReq) (res *v1.AdvertisementEditRes, err error)
		// Delete 删除广告
		Delete(ctx context.Context, req *v1.AdvertisementDeleteReq) (res *v1.AdvertisementDeleteRes, err error)
		// Online 上线广告
		Online(ctx context.Context, req *v1.AdvertisementOnlineReq) (res *v1.AdvertisementOnlineRes, err error)
		// Offline 下线广告
		Offline(ctx context.Context, req *v1.AdvertisementOfflineReq) (res *v1.AdvertisementOfflineRes, err error)
	}
)

var (
	localAdvertisement IAdvertisement
)

func Advertisement() IAdvertisement {
	if localAdvertisement == nil {
		panic("implement not found for interface IAdvertisement, forgot register?")
	}
	return localAdvertisement
}

func RegisterAdvertisement(i IAdvertisement) {
	localAdvertisement = i
}
