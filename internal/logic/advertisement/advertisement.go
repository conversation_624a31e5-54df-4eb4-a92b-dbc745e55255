package advertisement

import (
	"context"
	"fmt"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sAdvertisement struct{}

func init() {
	service.RegisterAdvertisement(New())
}

func New() *sAdvertisement {
	return &sAdvertisement{}
}

// =====================================================
// 广告位置管理
// =====================================================

// PositionList 广告位置列表
func (s *sAdvertisement) PositionList(ctx context.Context, req *v1.AdvertisementPositionListReq) (res *v1.AdvertisementPositionListRes, err error) {
	var positions []entity.AdvertisementPosition
	err = dao.AdvertisementPosition.Ctx(ctx).
		OrderDesc(dao.AdvertisementPosition.Columns().CreateTime).
		Scan(&positions)
	if err != nil {
		return nil, err
	}

	var list []v1.AdvertisementPositionItem
	for _, position := range positions {
		list = append(list, v1.AdvertisementPositionItem{
			Id:           int(position.Id),
			PositionName: position.PositionName,
			PositionCode: position.PositionCode,
			Remark:       position.Remark,
			CreateTime:   int64(position.CreateTime),
			UpdateTime:   int64(position.UpdateTime),
		})
	}

	return &v1.AdvertisementPositionListRes{
		List: list,
	}, nil
}

// PositionEdit 修改广告位置备注
func (s *sAdvertisement) PositionEdit(ctx context.Context, req *v1.AdvertisementPositionEditReq) (res *v1.AdvertisementPositionEditRes, err error) {
	// 检查广告位置是否存在
	count, err := dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告位置不存在")
	}

	// 更新备注
	_, err = dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().Id, req.Id).
		Data(do.AdvertisementPosition{
			Remark:     req.Remark,
			UpdateTime: gtime.Now().UnixMilli(),
		}).
		Update()
	if err != nil {
		return nil, err
	}

	return &v1.AdvertisementPositionEditRes{}, nil
}

// PositionOptions 广告位置选项
func (s *sAdvertisement) PositionOptions(ctx context.Context, req *v1.AdvertisementPositionOptionsReq) (res *v1.AdvertisementPositionOptionsRes, err error) {
	var positions []entity.AdvertisementPosition
	err = dao.AdvertisementPosition.Ctx(ctx).
		OrderAsc(dao.AdvertisementPosition.Columns().CreateTime).
		Scan(&positions)
	if err != nil {
		return nil, err
	}

	var list []v1.SelectOption
	for _, position := range positions {
		list = append(list, v1.SelectOption{
			Label: position.PositionName,
			Value: position.PositionCode,
		})
	}

	return &v1.AdvertisementPositionOptionsRes{
		List: list,
	}, nil
}

// =====================================================
// 广告管理
// =====================================================

// List 广告列表
func (s *sAdvertisement) List(ctx context.Context, req *v1.AdvertisementListReq) (res *v1.AdvertisementListRes, err error) {
	// 获取当前语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))

	// 构建查询条件
	md := dao.Advertisement.Ctx(ctx)

	// 根据广告位置过滤
	if req.PositionCode != nil && *req.PositionCode != "" {
		md = md.Where(dao.Advertisement.Columns().PositionCode, *req.PositionCode)
	}

	// 根据状态过滤
	if req.Status != nil {
		md = md.Where(dao.Advertisement.Columns().Status, *req.Status)
	}

	// 根据名称搜索（需要在多语言表中搜索）
	if req.Name != nil && *req.Name != "" {
		subQuery := dao.AdvertisementLanguages.Ctx(ctx).
			WhereLike(dao.AdvertisementLanguages.Columns().Title, "%"+*req.Name+"%").
			Fields(dao.AdvertisementLanguages.Columns().AdvertisementId)
		md = md.WhereIn(dao.Advertisement.Columns().Id, subQuery)
	}

	// 分页查询
	total, err := md.Count()
	if err != nil {
		return nil, err
	}

	var advertisements []entity.Advertisement
	err = md.Page(req.Current, req.PageSize).
		OrderDesc(dao.Advertisement.Columns().CreateTime).
		Scan(&advertisements)
	if err != nil {
		return nil, err
	}

	if len(advertisements) == 0 {
		return &v1.AdvertisementListRes{
			ListRes: v1.ListRes{
				Current: req.Current,
				Total:   total,
			},
			List: []v1.AdvertisementListItem{},
		}, nil
	}

	// 获取广告位置信息
	var positionCodes []string
	for _, ad := range advertisements {
		positionCodes = append(positionCodes, ad.PositionCode)
	}
	var positions []entity.AdvertisementPosition
	err = dao.AdvertisementPosition.Ctx(ctx).
		WhereIn(dao.AdvertisementPosition.Columns().PositionCode, positionCodes).
		Scan(&positions)
	if err != nil {
		return nil, err
	}

	// 构建位置映射
	positionMap := make(map[string]string)
	for _, position := range positions {
		positionMap[position.PositionCode] = position.PositionName
	}

	// 获取多语言信息
	var adIds []int
	for _, ad := range advertisements {
		adIds = append(adIds, int(ad.Id))
	}
	var languages []entity.AdvertisementLanguages
	err = dao.AdvertisementLanguages.Ctx(ctx).
		WhereIn(dao.AdvertisementLanguages.Columns().AdvertisementId, adIds).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 构建多语言映射
	languageMap := make(map[int]map[int]entity.AdvertisementLanguages)
	for _, lang := range languages {
		if languageMap[int(lang.AdvertisementId)] == nil {
			languageMap[int(lang.AdvertisementId)] = make(map[int]entity.AdvertisementLanguages)
		}
		languageMap[int(lang.AdvertisementId)][int(lang.LanguageId)] = lang
	}

	// 组装返回数据
	var list []v1.AdvertisementListItem
	for _, ad := range advertisements {
		item := v1.AdvertisementListItem{
			Id:           int(ad.Id),
			PositionCode: ad.PositionCode,
			PositionName: positionMap[ad.PositionCode],
			Status:       int(ad.Status),
			StatusText:   s.getStatusText(int(ad.Status)),
			SortOrder:    int(ad.SortOrder),
			StartTime:    int64(ad.StartTime),
			EndTime:      int64(ad.EndTime),
			CreateTime:   int64(ad.CreateTime),
			UpdateTime:   int64(ad.UpdateTime),
			LanguageArr:  []v1.LanguageArrItem{},
		}

		// 设置当前语言的标题
		if langData, ok := languageMap[int(ad.Id)][currentLang]; ok {
			item.Title = langData.Title
		}

		// 构建多语言支持信息
		for _, langType := range []int{consts.LangZh, consts.LangEn, consts.LangId} {
			isSupport := 0
			if _, ok := languageMap[int(ad.Id)][langType]; ok {
				isSupport = 1
			}
			item.LanguageArr = append(item.LanguageArr, v1.LanguageArrItem{
				LanguageType:     langType,
				LanguageTypeText: consts.GetLangText(langType),
				IsSupport:        isSupport,
			})
		}

		list = append(list, item)
	}

	return &v1.AdvertisementListRes{
		ListRes: v1.ListRes{
			Current: req.Current,
			Total:   total,
		},
		List: list,
	}, nil
}

// getStatusText 获取状态文本
func (s *sAdvertisement) getStatusText(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "启用"
	default:
		return "未知"
	}
}

// getDisplayTypeText 获取显示类型文本
func (s *sAdvertisement) getDisplayTypeText(displayType int) string {
	switch displayType {
	case 1:
		return "单图固定"
	case 2:
		return "多图轮播"
	default:
		return "未知"
	}
}

// One 广告详情
func (s *sAdvertisement) One(ctx context.Context, req *v1.AdvertisementOneReq) (res *v1.AdvertisementOneRes, err error) {
	// 获取广告基本信息
	var advertisement entity.Advertisement
	err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Scan(&advertisement)
	if err != nil {
		return nil, err
	}
	if advertisement.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告不存在")
	}

	// 获取多语言信息
	var languages []entity.AdvertisementLanguages
	err = dao.AdvertisementLanguages.Ctx(ctx).
		Where(dao.AdvertisementLanguages.Columns().AdvertisementId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 获取Banner图片信息
	var banners []entity.Banner
	err = dao.Banner.Ctx(ctx).
		Where(dao.Banner.Columns().AdvertisementId, req.Id).
		OrderAsc(dao.Banner.Columns().LanguageId).
		OrderAsc(dao.Banner.Columns().SortOrder).
		Scan(&banners)
	if err != nil {
		return nil, err
	}

	// 组装多语言信息
	var languageDetails []v1.AdvertisementLanguageDetail
	for _, lang := range languages {
		languageDetails = append(languageDetails, v1.AdvertisementLanguageDetail{
			LanguageId:       int(lang.LanguageId),
			LanguageText:     consts.GetLangText(int(lang.LanguageId)),
			Title:            lang.Title,
			Description:      lang.Description,
			DisplayType:      int(lang.DisplayType),
			DisplayTypeText:  s.getDisplayTypeText(int(lang.DisplayType)),
			CarouselInterval: int(lang.CarouselInterval),
		})
	}

	// 组装Banner信息
	var bannerDetails []v1.BannerDetail
	for _, banner := range banners {
		bannerDetails = append(bannerDetails, v1.BannerDetail{
			Id:         int(banner.Id),
			LanguageId: int(banner.LanguageId),
			ImageUrl:   banner.ImageUrl,
			LinkUrl:    banner.LinkUrl,
			SortOrder:  int(banner.SortOrder),
			Status:     int(banner.Status),
		})
	}

	return &v1.AdvertisementOneRes{
		Advertisement: v1.AdvertisementDetail{
			Id:           int(advertisement.Id),
			PositionCode: advertisement.PositionCode,
			SortOrder:    int(advertisement.SortOrder),
			Status:       int(advertisement.Status),
			StartTime:    int64(advertisement.StartTime),
			EndTime:      int64(advertisement.EndTime),
			CreateTime:   int64(advertisement.CreateTime),
			UpdateTime:   int64(advertisement.UpdateTime),
			Languages:    languageDetails,
			Banners:      bannerDetails,
		},
	}, nil
}

// Add 新增广告
func (s *sAdvertisement) Add(ctx context.Context, req *v1.AdvertisementAddReq) (res *v1.AdvertisementAddRes, err error) {
	// 验证时间范围
	if req.EndTime <= req.StartTime {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "结束时间必须大于开始时间")
	}

	// 验证广告位置是否存在
	count, err := dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().PositionCode, req.PositionCode).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告位置不存在")
	}

	// 验证多语言内容的语言类型不能重复
	languageMap := make(map[int]bool)
	for _, lang := range req.Languages {
		if languageMap[lang.LanguageId] {
			return nil, gerror.NewCode(gcode.CodeValidationFailed, fmt.Sprintf("语言类型%s重复", consts.GetLangText(lang.LanguageId)))
		}
		languageMap[lang.LanguageId] = true
	}

	// 获取当前管理员信息
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}

	var advertisementId int64
	err = dao.Advertisement.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入广告主表
		result, err1 := tx.Model(dao.Advertisement.Table()).Ctx(ctx).
			Data(do.Advertisement{
				PositionCode: req.PositionCode,
				SortOrder:    req.SortOrder,
				Status:       0, // 默认禁用状态
				StartTime:    req.StartTime,
				EndTime:      req.EndTime,
				AdminId:      admin.Id,
				CreateTime:   gtime.Now().UnixMilli(),
				UpdateTime:   gtime.Now().UnixMilli(),
			}).
			Insert()
		if err1 != nil {
			return err1
		}

		advertisementId, err1 = result.LastInsertId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var languages []do.AdvertisementLanguages
		for _, lang := range req.Languages {
			languages = append(languages, do.AdvertisementLanguages{
				AdvertisementId:  advertisementId,
				LanguageId:       lang.LanguageId,
				Title:            lang.Title,
				Description:      lang.Description,
				DisplayType:      lang.DisplayType,
				CarouselInterval: lang.CarouselInterval,
				AdminId:          admin.Id,
				CreateTime:       gtime.Now().UnixMilli(),
				UpdateTime:       gtime.Now().UnixMilli(),
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		// 插入Banner图片
		var banners []do.Banner
		for _, banner := range req.Banners {
			banners = append(banners, do.Banner{
				AdvertisementId: advertisementId,
				LanguageId:      banner.LanguageId,
				ImageUrl:        banner.ImageUrl,
				LinkUrl:         banner.LinkUrl,
				SortOrder:       banner.SortOrder,
				Status:          1, // 默认启用
				AdminId:         admin.Id,
				CreateTime:      gtime.Now().UnixMilli(),
				UpdateTime:      gtime.Now().UnixMilli(),
			})
		}

		if len(banners) > 0 {
			_, err1 = tx.Model(dao.Banner.Table()).Ctx(ctx).
				Data(banners).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &v1.AdvertisementAddRes{
		Id: int(advertisementId),
	}, nil
}

// Edit 编辑广告
func (s *sAdvertisement) Edit(ctx context.Context, req *v1.AdvertisementEditReq) (res *v1.AdvertisementEditRes, err error) {
	// 验证时间范围
	if req.EndTime <= req.StartTime {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "结束时间必须大于开始时间")
	}

	// 验证广告是否存在
	count, err := dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告不存在")
	}

	// 验证广告位置是否存在
	count, err = dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().PositionCode, req.PositionCode).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告位置不存在")
	}

	// 验证多语言内容的语言类型不能重复
	languageMap := make(map[int]bool)
	for _, lang := range req.Languages {
		if languageMap[lang.LanguageId] {
			return nil, gerror.NewCode(gcode.CodeValidationFailed, fmt.Sprintf("语言类型%s重复", consts.GetLangText(lang.LanguageId)))
		}
		languageMap[lang.LanguageId] = true
	}

	// 获取当前管理员信息
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}

	err = dao.Advertisement.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新广告主表
		_, err1 := tx.Model(dao.Advertisement.Table()).Ctx(ctx).
			Where(dao.Advertisement.Columns().Id, req.Id).
			Data(do.Advertisement{
				PositionCode: req.PositionCode,
				SortOrder:    req.SortOrder,
				StartTime:    req.StartTime,
				EndTime:      req.EndTime,
				UpdateTime:   gtime.Now().UnixMilli(),
			}).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除原有的多语言内容
		_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
			Where(dao.AdvertisementLanguages.Columns().AdvertisementId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var languages []do.AdvertisementLanguages
		for _, lang := range req.Languages {
			languages = append(languages, do.AdvertisementLanguages{
				AdvertisementId:  req.Id,
				LanguageId:       lang.LanguageId,
				Title:            lang.Title,
				Description:      lang.Description,
				DisplayType:      lang.DisplayType,
				CarouselInterval: lang.CarouselInterval,
				AdminId:          admin.Id,
				CreateTime:       gtime.Now().UnixMilli(),
				UpdateTime:       gtime.Now().UnixMilli(),
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		// 删除原有的Banner图片
		_, err1 = tx.Model(dao.Banner.Table()).Ctx(ctx).
			Where(dao.Banner.Columns().AdvertisementId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的Banner图片
		var banners []do.Banner
		for _, banner := range req.Banners {
			banners = append(banners, do.Banner{
				AdvertisementId: req.Id,
				LanguageId:      banner.LanguageId,
				ImageUrl:        banner.ImageUrl,
				LinkUrl:         banner.LinkUrl,
				SortOrder:       banner.SortOrder,
				Status:          1, // 默认启用
				AdminId:         admin.Id,
				CreateTime:      gtime.Now().UnixMilli(),
				UpdateTime:      gtime.Now().UnixMilli(),
			})
		}

		if len(banners) > 0 {
			_, err1 = tx.Model(dao.Banner.Table()).Ctx(ctx).
				Data(banners).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &v1.AdvertisementEditRes{}, nil
}

// Delete 删除广告
func (s *sAdvertisement) Delete(ctx context.Context, req *v1.AdvertisementDeleteReq) (res *v1.AdvertisementDeleteRes, err error) {
	// 验证广告是否存在
	count, err := dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告不存在")
	}

	err = dao.Advertisement.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除Banner图片
		_, err1 := tx.Model(dao.Banner.Table()).Ctx(ctx).
			Where(dao.Banner.Columns().AdvertisementId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除多语言内容
		_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
			Where(dao.AdvertisementLanguages.Columns().AdvertisementId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除广告主表
		_, err1 = tx.Model(dao.Advertisement.Table()).Ctx(ctx).
			Where(dao.Advertisement.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &v1.AdvertisementDeleteRes{}, nil
}

// Online 上线广告
func (s *sAdvertisement) Online(ctx context.Context, req *v1.AdvertisementOnlineReq) (res *v1.AdvertisementOnlineRes, err error) {
	// 验证广告是否存在
	var advertisement entity.Advertisement
	err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Scan(&advertisement)
	if err != nil {
		return nil, err
	}
	if advertisement.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告不存在")
	}

	// 检查是否已经是上线状态
	if advertisement.Status == 1 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告已经是上线状态")
	}

	// 检查同一个广告位置是否已经有上线的广告（业务规则：同一个广告位置最多只能有一个上线状态的广告）
	count, err := dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().PositionCode, advertisement.PositionCode).
		Where(dao.Advertisement.Columns().Status, 1).
		WhereNot(dao.Advertisement.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "该广告位置已有上线的广告，请先下线其他广告")
	}

	// 更新状态为上线
	_, err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Data(do.Advertisement{
			Status:     1,
			UpdateTime: gtime.Now().UnixMilli(),
		}).
		Update()
	if err != nil {
		return nil, err
	}

	return &v1.AdvertisementOnlineRes{}, nil
}

// Offline 下线广告
func (s *sAdvertisement) Offline(ctx context.Context, req *v1.AdvertisementOfflineReq) (res *v1.AdvertisementOfflineRes, err error) {
	// 验证广告是否存在
	var advertisement entity.Advertisement
	err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Scan(&advertisement)
	if err != nil {
		return nil, err
	}
	if advertisement.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告不存在")
	}

	// 检查是否已经是下线状态
	if advertisement.Status == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "广告已经是下线状态")
	}

	// 更新状态为下线
	_, err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.Id).
		Data(do.Advertisement{
			Status:     0,
			UpdateTime: gtime.Now().UnixMilli(),
		}).
		Update()
	if err != nil {
		return nil, err
	}

	return &v1.AdvertisementOfflineRes{}, nil
}
