
# CLI tool, only in development environment.
# https://goframe.org/pages/viewpage.action?pageId=3673173
gfcli:
  gen:
    dao:
      - link: "mysql:root:Ha123456@tcp(***************:3306)/admin"
        #tables: "user"
        descriptionTag: true #用于指定是否为数据模型结构体属性增加desription的标签，内容为对应的数据表字段注释。
        noModelComment: true #用于指定是否关闭数据模型结构体属性的注释自动生成，内容为数据表对应字段的注释。
        group: "admin"
        daoPath: "dao/admin"
        doPath: "model/do/admin"
        entityPath: "model/entity/admin"
        typeMapping:
          decimal:
            type: "decimal.Decimal"
            import: "github.com/shopspring/decimal"
        #  json:
        #    type: map[string]interface{}

      - link: "mysql:root:Ha123456@tcp(***************:3306)/islamic_content_svc"
        #tables: "user"
        descriptionTag: true #用于指定是否为数据模型结构体属性增加desription的标签，内容为对应的数据表字段注释。
        noModelComment: true #用于指定是否关闭数据模型结构体属性的注释自动生成，内容为数据表对应字段的注释。
        #gf v2.5才开始支持typeMapping
        group: "islamic_content_svc"
        daoPath: "dao/islamic_content_svc"
        doPath: "model/do/islamic_content_svc"
        entityPath: "model/entity/islamic_content_svc"
        typeMapping:
          decimal:
            type: "decimal.Decimal"
            import: "github.com/shopspring/decimal"
        #  json:
        #    type: map[string]interface{}
      - link: "mysql:root:Ha123456@tcp(***************:3306)/user_account_svc"
        #tables: "user"
        descriptionTag: true #用于指定是否为数据模型结构体属性增加desription的标签，内容为对应的数据表字段注释。
        noModelComment: true #用于指定是否关闭数据模型结构体属性的注释自动生成，内容为数据表对应字段的注释。
        #gf v2.5才开始支持typeMapping
        group: "user_account_svc"
        daoPath: "dao/user_account_svc"
        doPath: "model/do/user_account_svc"
        entityPath: "model/entity/user_account_svc"
        typeMapping:
          decimal:
            type: "decimal.Decimal"
            import: "github.com/shopspring/decimal"
        #  json:
        #    type: map[string]interface{}
  build:
    arch: "amd64"
    # arch: "amd64"
    system: "linux"
    name: "halal-admin"

    packSrc: "resource/i18n,resource/public,resource/template"