create table islamic_content_svc.banner
(
    id          int unsigned auto_increment comment '主键ID'
        primary key,
    language_id tinyint unsigned default '0'    not null comment '语言ID: 0-中文, 1-英文, 2-印尼语',
    banner_type varchar(20)      default 'home' not null comment 'Banner类型: home， ibadah',
    title       varchar(255)     default ''     not null comment '广告标题',
    description text                            null comment '广告描述',
    image_url   varchar(500)     default ''     not null comment '广告图片URL',
    link_url    varchar(500)     default ''     not null comment '跳转链接URL',
    sort_order  int unsigned     default '0'    not null comment '排序权重，数字越小越靠前',
    status      tinyint unsigned default '1'    not null comment '状态: 0-禁用, 1-启用',
    start_time  bigint unsigned  default '0'    not null comment '开始时间戳(毫秒)',
    end_time    bigint unsigned  default '0'    not null comment '结束时间戳(毫秒)',
    admin_id    int unsigned     default '0'    not null comment '创建管理员ID',
    create_time bigint unsigned  default '0'    not null comment '创建时间(毫秒时间戳)',
    update_time bigint unsigned  default '0'    not null comment '更新时间(毫秒时间戳)'
)
    comment 'Banner广告表' collate = utf8mb4_unicode_ci;

create index idx_banner_type
    on islamic_content_svc.banner (banner_type);

create index idx_language_type_status_sort_order
    on islamic_content_svc.banner (language_id, banner_type, status, sort_order);

create index idx_status_time
    on islamic_content_svc.banner (status, start_time, end_time);

create index idx_time_range
    on islamic_content_svc.banner (start_time, end_time);

create table islamic_content_svc.banner_stats
(
    id          bigint unsigned auto_increment comment '主键ID'
        primary key,
    banner_id   int unsigned                not null comment '广告ID',
    user_id     bigint unsigned default '0' not null comment '用户ID，0表示未登录用户',
    device_id   varchar(100)    default ''  not null comment '设备唯一标识',
    ip_address  varchar(45)     default ''  not null comment 'IP地址',
    user_agent  varchar(500)    default ''  not null comment '用户代理信息',
    create_time bigint unsigned default '0' not null comment '操作时间(毫秒时间戳)'
)
    comment 'Banner统计表' collate = utf8mb4_unicode_ci;

create index idx_banner_id
    on islamic_content_svc.banner_stats (banner_id);

create index idx_banner_time
    on islamic_content_svc.banner_stats (banner_id, create_time);

create index idx_created_at
    on islamic_content_svc.banner_stats (create_time);

create index idx_user_id
    on islamic_content_svc.banner_stats (user_id);

