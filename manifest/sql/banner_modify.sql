-- Banner表修改SQL文件
-- 1. 创建广告位置表
-- 2. 修改现有banner表为advertisement表
-- 3. 创建新的banner图片表

-- =====================================================
-- 1. 创建广告位置表 (advertisement_position)
-- =====================================================
CREATE TABLE islamic_content_svc.advertisement_position
(
    id            INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    position_name VARCHAR(100) DEFAULT '' NOT NULL COMMENT '广告位名称，不可修改',
    position_code VARCHAR(50)  DEFAULT '' NOT NULL COMMENT '位置编码，自动生成，不可修改',
    remark        VARCHAR(500) DEFAULT '' NOT NULL COMMENT '备注，对该广告位置的备注，可修改',
    create_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)',
    UNIQUE KEY uk_position_code (position_code)
) COMMENT '广告位置表' COLLATE = utf8mb4_unicode_ci;

-- 插入初始数据
INSERT INTO islamic_content_svc.advertisement_position (position_name, position_code, remark, create_time, update_time) VALUES
('首页广告位', 'home', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('礼拜页面广告位', 'ibadah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- =====================================================
-- 2. 修改现有banner表为advertisement表，支持多语言
-- =====================================================

-- 重命名banner表为advertisement表
RENAME TABLE islamic_content_svc.banner TO islamic_content_svc.advertisement;

-- 修改advertisement表结构
ALTER TABLE islamic_content_svc.advertisement 
    CHANGE COLUMN banner_type position_code VARCHAR(50) DEFAULT 'home' NOT NULL COMMENT '广告位置编码',
    ADD COLUMN display_type TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '显示类型: 1-单图固定, 2-多图轮播' AFTER position_code,
    ADD COLUMN carousel_interval INT UNSIGNED DEFAULT '3' NOT NULL COMMENT '轮播间隔时间(秒)，仅多图轮播时有效' AFTER display_type,
    MODIFY COLUMN title VARCHAR(255) DEFAULT '' NOT NULL COMMENT '广告名称(多语言)',
    DROP COLUMN image_url,
    DROP COLUMN link_url;

-- 更新表注释
ALTER TABLE islamic_content_svc.advertisement COMMENT = '广告表(支持多语言)';

-- 删除旧索引
DROP INDEX idx_banner_type ON islamic_content_svc.advertisement;

-- 创建新索引
CREATE INDEX idx_position_code ON islamic_content_svc.advertisement (position_code);
CREATE INDEX idx_language_position_status_sort_order ON islamic_content_svc.advertisement (language_id, position_code, status, sort_order);

-- =====================================================
-- 3. 创建新的banner图片表
-- =====================================================
CREATE TABLE islamic_content_svc.banner
(
    id               INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    advertisement_id INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '广告ID',
    language_id      TINYINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    image_url        VARCHAR(500) DEFAULT '' NOT NULL COMMENT '广告图片URL',
    link_url         VARCHAR(500) DEFAULT '' NOT NULL COMMENT '跳转链接URL',
    sort_order       INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '排序权重，数字越小越靠前',
    status           TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '状态: 0-禁用, 1-启用',
    admin_id         INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建管理员ID',
    create_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)'
) COMMENT 'Banner图片表' COLLATE = utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_advertisement_id ON islamic_content_svc.banner (advertisement_id);
CREATE INDEX idx_advertisement_language_status_sort ON islamic_content_svc.banner (advertisement_id, language_id, status, sort_order);
CREATE INDEX idx_status ON islamic_content_svc.banner (status);

-- =====================================================
-- 4. 更新banner_stats表的外键关联
-- =====================================================
ALTER TABLE islamic_content_svc.banner_stats 
    CHANGE COLUMN banner_id advertisement_id INT UNSIGNED NOT NULL COMMENT '广告ID';

-- 更新索引
DROP INDEX idx_banner_id ON islamic_content_svc.banner_stats;
DROP INDEX idx_banner_time ON islamic_content_svc.banner_stats;

CREATE INDEX idx_advertisement_id ON islamic_content_svc.banner_stats (advertisement_id);
CREATE INDEX idx_advertisement_time ON islamic_content_svc.banner_stats (advertisement_id, create_time);

-- 更新表注释
ALTER TABLE islamic_content_svc.banner_stats COMMENT = '广告统计表';
