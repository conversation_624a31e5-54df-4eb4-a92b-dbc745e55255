-- Banner表修改SQL文件
-- 1. 创建广告位置表
-- 2. 创建新的advertisement表（广告表）
-- 3. 创建advertisement_languages表（广告多语言表）
-- 4. 修改现有banner表为新的banner表（图片表）

-- =====================================================
-- 1. 创建广告位置表 (advertisement_position)
-- =====================================================
CREATE TABLE islamic_content_svc.advertisement_position
(
    id            INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    position_name VARCHAR(100) DEFAULT '' NOT NULL COMMENT '广告位名称，不可修改',
    position_code VARCHAR(50)  DEFAULT '' NOT NULL COMMENT '位置编码，自动生成，不可修改',
    remark        VARCHAR(500) DEFAULT '' NOT NULL COMMENT '备注，对该广告位置的备注，可修改',
    create_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)',
    UNIQUE KEY uk_position_code (position_code)
) COMMENT '广告位置表' COLLATE = utf8mb4_unicode_ci;

-- 插入初始数据
INSERT INTO islamic_content_svc.advertisement_position (position_name, position_code, remark, create_time, update_time) VALUES
('Home Page', 'home', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('Ibadah Page', 'ibadah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- =====================================================
-- 2. 创建新的advertisement表（广告主表）
-- =====================================================
CREATE TABLE islamic_content_svc.advertisement
(
    id            INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    position_code VARCHAR(50) DEFAULT 'home' NOT NULL COMMENT '广告位置编码',
    sort_order    INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '排序权重，数字越小越靠前',
    status        TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '状态: 0-禁用, 1-启用',
    start_time    BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '开始时间戳(毫秒)',
    end_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '结束时间戳(毫秒)',
    admin_id      INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建管理员ID',
    create_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)'
) COMMENT '广告主表(语言无关信息)' COLLATE = utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_position_code ON islamic_content_svc.advertisement (position_code);
CREATE INDEX idx_position_status_sort_order ON islamic_content_svc.advertisement (position_code, status, sort_order);
CREATE INDEX idx_status_time ON islamic_content_svc.advertisement (status, start_time, end_time);
CREATE INDEX idx_time_range ON islamic_content_svc.advertisement (start_time, end_time);

-- =====================================================
-- 3. 创建advertisement_languages表（广告多语言表）
-- =====================================================
CREATE TABLE islamic_content_svc.advertisement_languages
(
    id                INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    advertisement_id  INT UNSIGNED NOT NULL COMMENT '广告ID',
    language_id       TINYINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    title             VARCHAR(255) DEFAULT '' NOT NULL COMMENT '广告名称',
    description       TEXT NULL COMMENT '广告描述',
    display_type      TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '显示类型: 1-单图固定, 2-多图轮播',
    carousel_interval INT UNSIGNED DEFAULT '3' NOT NULL COMMENT '轮播间隔时间(秒)，仅多图轮播时有效',
    admin_id          INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建管理员ID',
    create_time       BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time       BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)',
    UNIQUE KEY uk_advertisement_language (advertisement_id, language_id)
) COMMENT '广告多语言表(语言相关信息)' COLLATE = utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_advertisement_id ON islamic_content_svc.advertisement_languages (advertisement_id);
CREATE INDEX idx_language_id ON islamic_content_svc.advertisement_languages (language_id);

-- =====================================================
-- 4. 修改现有banner表为新的banner表（图片表）
-- =====================================================

-- 重命名banner表为banner_temp表（临时表）
RENAME TABLE islamic_content_svc.banner TO islamic_content_svc.banner_temp;

-- 创建新的banner表
CREATE TABLE islamic_content_svc.banner
(
    id               INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    advertisement_id INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '广告ID',
    language_id      TINYINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    image_url        VARCHAR(500) DEFAULT '' NOT NULL COMMENT 'Banner图片URL',
    link_url         VARCHAR(500) DEFAULT '' NOT NULL COMMENT '跳转链接URL',
    sort_order       INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '图片排序权重，数字越小越靠前',
    status           TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '状态: 0-禁用, 1-启用',
    admin_id         INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建管理员ID',
    create_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)'
) COMMENT 'Banner图片表' COLLATE = utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_advertisement_id ON islamic_content_svc.banner (advertisement_id);
CREATE INDEX idx_advertisement_language_status_sort ON islamic_content_svc.banner (advertisement_id, language_id, status, sort_order);
CREATE INDEX idx_status ON islamic_content_svc.banner (status);

-- =====================================================
-- 5. 更新banner_stats表的外键关联
-- =====================================================
ALTER TABLE islamic_content_svc.banner_stats
    CHANGE COLUMN banner_id advertisement_id INT UNSIGNED NOT NULL COMMENT '广告ID';

-- 更新索引
DROP INDEX idx_banner_id ON islamic_content_svc.banner_stats;
DROP INDEX idx_banner_time ON islamic_content_svc.banner_stats;

CREATE INDEX idx_advertisement_id ON islamic_content_svc.banner_stats (advertisement_id);
CREATE INDEX idx_advertisement_time ON islamic_content_svc.banner_stats (advertisement_id, create_time);

-- 更新表注释
ALTER TABLE islamic_content_svc.banner_stats COMMENT = '广告统计表';

-- =====================================================
-- 6. 数据迁移（可选）
-- =====================================================
-- 如果需要将现有数据迁移到新结构，可以执行以下步骤：
-- 1. 先将现有banner_temp数据迁移到advertisement表和advertisement_languages表
-- 2. 再将图片相关数据迁移到新的banner表中
-- 3. 最后删除临时表banner_temp

-- 示例迁移脚本（需要根据实际数据调整）：
-- INSERT INTO islamic_content_svc.advertisement (position_code, sort_order, status, start_time, end_time, admin_id, create_time, update_time)
-- SELECT DISTINCT banner_type, sort_order, status, start_time, end_time, admin_id, create_time, update_time FROM islamic_content_svc.banner_temp;

-- INSERT INTO islamic_content_svc.advertisement_languages (advertisement_id, language_id, title, description, display_type, carousel_interval, admin_id, create_time, update_time)
-- SELECT a.id, b.language_id, b.title, b.description, 1, 3, b.admin_id, b.create_time, b.update_time
-- FROM islamic_content_svc.advertisement a
-- JOIN islamic_content_svc.banner_temp b ON a.position_code = b.banner_type;

-- INSERT INTO islamic_content_svc.banner (advertisement_id, language_id, image_url, link_url, sort_order, status, admin_id, create_time, update_time)
-- SELECT a.id, b.language_id, b.image_url, b.link_url, 0, 1, b.admin_id, b.create_time, b.update_time
-- FROM islamic_content_svc.advertisement a
-- JOIN islamic_content_svc.banner_temp b ON a.position_code = b.banner_type;

-- DROP TABLE islamic_content_svc.banner_temp;

-- =====================================================
-- 数据结构说明
-- =====================================================
/*
数据结构关系：
1. advertisement_position (广告位置表) - 不支持多语言
   - 固定的广告位置，目前支持 home 和 ibadah 两种编码

2. advertisement (广告主表) - 语言无关信息
   - 存储广告的基础信息：位置、状态、时间范围、排序等
   - 通过 position_code 关联到广告位置

3. advertisement_languages (广告多语言表) - 语言相关信息
   - 每种语言都有独立的记录
   - 每种语言可以设置不同的：
     * title (广告名称)
     * description (广告描述)
     * display_type (显示类型：单图固定/多图轮播)
     * carousel_interval (轮播间隔时间)
   - 通过 advertisement_id 关联到广告主表

4. banner_image (Banner图片表) - 支持多语言和多图片
   - 每个广告的每种语言可以有多张Banner图片
   - 每张Banner图片都有独立的 image_url 和 link_url
   - 通过 advertisement_id 和 language_id 关联到对应的广告

示例数据结构：
- 广告位置: home (首页)
- 广告主表: 位置=home, 状态=启用, 排序=1
- 广告多语言:
  * 中文: 标题="春节促销", 显示类型=多图轮播, 间隔=5秒
  * 英文: 标题="Spring Sale", 显示类型=单图固定, 间隔=3秒
- Banner图片:
  * 中文: 图片1, 图片2, 图片3
  * 英文: 图片1
*/
