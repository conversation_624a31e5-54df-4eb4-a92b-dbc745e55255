-- Banner表修改SQL文件
-- 1. 创建广告位置表
-- 2. 修改现有banner表为advertisement表
-- 3. 创建新的banner图片表

-- =====================================================
-- 1. 创建广告位置表 (advertisement_position)
-- =====================================================
CREATE TABLE islamic_content_svc.advertisement_position
(
    id            INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    position_name VARCHAR(100) DEFAULT '' NOT NULL COMMENT '广告位名称，不可修改',
    position_code VARCHAR(50)  DEFAULT '' NOT NULL COMMENT '位置编码，自动生成，不可修改',
    remark        VARCHAR(500) DEFAULT '' NOT NULL COMMENT '备注，对该广告位置的备注，可修改',
    create_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time   BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)',
    UNIQUE KEY uk_position_code (position_code)
) COMMENT '广告位置表' COLLATE = utf8mb4_unicode_ci;

-- 插入初始数据
INSERT INTO islamic_content_svc.advertisement_position (position_name, position_code, remark, create_time, update_time) VALUES
('首页广告位', 'home', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('礼拜页面广告位', 'ibadah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- =====================================================
-- 2. 修改现有banner表为advertisement表，支持多语言
-- =====================================================

-- 重命名banner表为advertisement表
RENAME TABLE islamic_content_svc.banner TO islamic_content_svc.advertisement;

-- 修改advertisement表结构
ALTER TABLE islamic_content_svc.advertisement
    CHANGE COLUMN banner_type position_code VARCHAR(50) DEFAULT 'home' NOT NULL COMMENT '广告位置编码',
    ADD COLUMN display_type TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '显示类型: 1-单图固定, 2-多图轮播(每种语言独立设置)' AFTER position_code,
    ADD COLUMN carousel_interval INT UNSIGNED DEFAULT '3' NOT NULL COMMENT '轮播间隔时间(秒)，仅多图轮播时有效(每种语言独立设置)' AFTER display_type,
    MODIFY COLUMN title VARCHAR(255) DEFAULT '' NOT NULL COMMENT '广告名称(每种语言独立设置)',
    DROP COLUMN image_url,
    DROP COLUMN link_url;

-- 更新表注释
ALTER TABLE islamic_content_svc.advertisement COMMENT = '广告表(支持多语言，每种语言的显示类型、轮播间隔、标题都独立设置)';

-- 删除旧索引
DROP INDEX idx_banner_type ON islamic_content_svc.advertisement;

-- 创建新索引
CREATE INDEX idx_position_code ON islamic_content_svc.advertisement (position_code);
CREATE INDEX idx_language_position_status_sort_order ON islamic_content_svc.advertisement (language_id, position_code, status, sort_order);

-- =====================================================
-- 3. 创建新的banner图片表
-- =====================================================
CREATE TABLE islamic_content_svc.banner
(
    id               INT UNSIGNED AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    advertisement_id INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '广告ID',
    language_id      TINYINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    image_url        VARCHAR(500) DEFAULT '' NOT NULL COMMENT '广告图片URL',
    link_url         VARCHAR(500) DEFAULT '' NOT NULL COMMENT '跳转链接URL',
    sort_order       INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '排序权重，数字越小越靠前',
    status           TINYINT UNSIGNED DEFAULT '1' NOT NULL COMMENT '状态: 0-禁用, 1-启用',
    admin_id         INT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建管理员ID',
    create_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '创建时间(毫秒时间戳)',
    update_time      BIGINT UNSIGNED DEFAULT '0' NOT NULL COMMENT '更新时间(毫秒时间戳)'
) COMMENT 'Banner图片表' COLLATE = utf8mb4_unicode_ci;

-- 创建索引
CREATE INDEX idx_advertisement_id ON islamic_content_svc.banner (advertisement_id);
CREATE INDEX idx_advertisement_language_status_sort ON islamic_content_svc.banner (advertisement_id, language_id, status, sort_order);
CREATE INDEX idx_status ON islamic_content_svc.banner (status);

-- =====================================================
-- 4. 更新banner_stats表的外键关联
-- =====================================================
ALTER TABLE islamic_content_svc.banner_stats 
    CHANGE COLUMN banner_id advertisement_id INT UNSIGNED NOT NULL COMMENT '广告ID';

-- 更新索引
DROP INDEX idx_banner_id ON islamic_content_svc.banner_stats;
DROP INDEX idx_banner_time ON islamic_content_svc.banner_stats;

CREATE INDEX idx_advertisement_id ON islamic_content_svc.banner_stats (advertisement_id);
CREATE INDEX idx_advertisement_time ON islamic_content_svc.banner_stats (advertisement_id, create_time);

-- 更新表注释
ALTER TABLE islamic_content_svc.banner_stats COMMENT = '广告统计表';

-- =====================================================
-- 数据结构说明
-- =====================================================
/*
数据结构关系：
1. advertisement_position (广告位置表) - 不支持多语言
   - 固定的广告位置，目前支持 home 和 ibadah 两种编码

2. advertisement (广告表) - 支持多语言
   - 每种语言都有独立的记录
   - 每种语言可以设置不同的：
     * title (广告名称)
     * display_type (显示类型：单图固定/多图轮播)
     * carousel_interval (轮播间隔时间)
   - 通过 position_code 关联到广告位置

3. banner (广告图片表) - 支持多语言和多图片
   - 每个广告的每种语言可以有多张图片
   - 每张图片都有独立的 image_url 和 link_url
   - 通过 advertisement_id 和 language_id 关联到对应的广告

示例数据结构：
- 广告位置: home (首页)
- 广告 (中文): 标题="春节促销", 显示类型=多图轮播, 间隔=5秒
- 广告 (英文): 标题="Spring Sale", 显示类型=单图固定, 间隔=3秒
- 图片 (中文): 图片1, 图片2, 图片3
- 图片 (英文): 图片1
*/
